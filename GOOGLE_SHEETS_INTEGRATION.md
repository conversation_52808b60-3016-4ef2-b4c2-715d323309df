# Google Sheets Integration - Production Setup

## Overview
This integration automatically tracks e-book purchases in Google Sheets and prevents duplicate email sending.

## Required Environment Variables
```env
GOOGLE_SHEET_ID=your_google_sheet_id
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
```

## Google Sheet Structure
Your Google Sheet must have these columns (in this exact order):
- `Timestamp` - When the form was submitted/payment completed
- `Name` - Customer name
- `Email` - Customer email (normalized to lowercase)
- `UniqueID` - Unique identifier for tracking
- `Status` - "Pending Payment" or "Payment Complete"
- `OrderID` - Order ID from payment gateway
- `PaymentID` - Payment ID from payment gateway
- `EmailSent` - "No" or "Yes" to track email delivery

## How It Works

### 1. Form Submission (Checkout)
When a customer initiates checkout via `/api/checkout`:
- Creates a "Pending Payment" entry in Google Sheets
- Generates a unique ID for tracking
- Proceeds with payment gateway

### 2. Payment Completion (Webhook)
When payment succeeds via `/api/payments/webhooks`:
- Finds the "Pending Payment" record by email
- Updates status to "Payment Complete"
- Adds OrderID and PaymentID
- Sends e-book email
- Marks email as sent

### 3. Duplicate Prevention
- Each payment has a unique key: `${orderId}-${paymentId}-${email}`
- Checks if email was already sent before processing
- Prevents multiple emails for the same payment

## Production Files

### Core Files:
- `/app/api/checkout/route.ts` - Creates initial Google Sheets entry
- `/app/api/payments/webhooks/route.ts` - Handles payment completion
- `/app/lib/google-sheets.ts` - Google Sheets operations

### Key Functions:
- `addFormSubmission()` - Creates pending payment record
- `updatePaymentStatusByEmail()` - Updates to payment complete
- `wasEmailAlreadySent()` - Checks if email already sent
- `markEmailAsSent()` - Marks email as delivered

## Error Handling
- If Google Sheets is unavailable, checkout still proceeds
- If webhook can't find pending payment, no email is sent
- Failed emails don't mark payment as processed (allows retry)
- All errors are logged for monitoring

## Monitoring
Check your application logs for:
- "No pending payment record found" - indicates email/webhook mismatch
- Failed Google Sheets operations
- Email sending failures

## Setup Instructions
1. Create a Google Sheet with the required columns
2. Set up Google Service Account with Sheets API access
3. Add environment variables to your deployment
4. Deploy and test with a real payment

The system is now production-ready and will automatically track all e-book purchases in your Google Sheet while preventing duplicate emails.
