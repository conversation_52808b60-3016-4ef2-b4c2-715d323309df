@import "tailwindcss";

@layer base {
  :root {
    --background: 220 27% 4%;
    --foreground: 0 0% 95%;

    --card: 220 25% 8%;
    --card-foreground: 0 0% 95%;

    --popover: 220 25% 8%;
    --popover-foreground: 0 0% 95%;

    --primary: 260 100% 68%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 263 85% 75%;

    --secondary: 220 25% 12%;
    --secondary-foreground: 0 0% 85%;

    --muted: 220 25% 10%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 263 85% 65%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 220 25% 15%;
    --input: 220 25% 15%;
    --ring: 263 85% 65%;

    /* Custom Dominion colors */
    --dominion-purple: 263 85% 65%;
    --dominion-purple-dark: 263 85% 45%;
    --dominion-purple-light: 263 85% 75%;
    --dominion-dark: 220 27% 4%;
    --dominion-dark-lighter: 220 25% 8%;
    --dominion-gray: 240 5% 64.9%;

    /* Gradients */
    --gradient-primary: linear-gradient(#fff, #925bff);
    --gradient-primary-2: linear-gradient(135deg, #fff, #925bff 68%);
    --gradient-hero: linear-gradient(180deg, hsl(var(--background)), hsl(220 25% 6%));
    --gradient-card: linear-gradient(145deg, hsl(var(--card)), hsl(220 25% 10%));

    /* Shadows */
    --shadow-purple: 0 10px 30px -10px hsl(var(--dominion-purple) / 0.3);
    --shadow-glow: 0 0 40px hsl(var(--dominion-purple) / 0.2);
    --shadow-card: 0 8px 32px hsl(220 27% 2% / 0.8);

    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-glow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .light {
    /* Light mode overrides if needed */
    --background: 0 0% 100%;
    --foreground: 220 27% 4%;
    --card: 0 0% 100%;
    --card-foreground: 220 27% 4%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-family: var(--font-sans);
    background: var(--gradient-hero);
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background-color: hsl(var(--muted));
  }

  ::-webkit-scrollbar-thumb {
    background-color: hsl(var(--primary));
    border-radius: 9999px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--primary) / 0.8);
  }
}

@layer components {
  .gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .glow-button {
    position: relative;
    overflow: hidden;
    transition: var(--transition-glow);
  }

  .glow-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-smooth);
  }

  .glow-button:hover::before {
    left: 100%;
  }

  .glow-button:hover {
    box-shadow: var(--shadow-purple);
  }

  .network-pattern {
    background-image: radial-gradient(circle at 1px 1px, hsl(var(--primary) / 0.15) 1px, transparent 0);
    background-size: 20px 20px;
  }

  .floating-animation {
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }

  .fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
  }

  @keyframes fadeInUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

@theme {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-plus-jakarta-sans);
}

