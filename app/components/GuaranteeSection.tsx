export const GuaranteeSection = () => {
  return (
    <section className="py-20 px-4 bg-secondary/20">
      <div className="container mx-auto max-w-4xl">
        <div className="text-center mb-12 fade-in-up">
          <h2 className="text-4xl lg:text-5xl font-bold mb-6">
            💸 No-Risk <span className="gradient-text">Guarantee</span>
          </h2>
        </div>

        <div className="bg-gradient-card border border-primary/20 rounded-2xl p-8 fade-in-up" style={{animationDelay: '0.2s'}}>
          <div className="space-y-6">
            <p className="text-lg font-semibold text-primary text-center">
              I'm not here to waste your time.
            </p>
            
            <p className="text-lg text-foreground/90 leading-relaxed">
              This book was built from real experiments, lived experiences, and behavioral science—not recycled fluff. I'm so confident in the practical tools inside The Dominion Network Playbook, that here's my commitment:
            </p>

            <div className="bg-primary/10 border border-primary/30 rounded-xl p-6">
              <h3 className="text-xl font-bold text-primary mb-4">60-Day Challenge:</h3>
              <div className="space-y-3 text-foreground/90">
                <p>✓ Use these frameworks, scripts, and prompts for 60 days.</p>
                <p>✓ If you don't feel more connected, more memorable, and more strategically visible</p>
                <p>✓ If you don't walk away with at least five real-world wins (a collaboration, a referral, an invite, a client, a hire...)</p>
                <p className="font-semibold text-primary">
                  Email me, show me you tried, and I'll return your money.
                </p>
              </div>
            </div>

            <div className="text-center space-y-4">
              <p className="text-lg font-semibold">
                No drama. Just proof.
              </p>
              
              <div className="space-y-2 text-muted-foreground">
                <p>Because I didn't write this to make a sale.</p>
                <p className="text-lg text-primary font-semibold">
                  I wrote it to give you the network I wish I had when I started.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-12 grid md:grid-cols-3 gap-6 fade-in-up" style={{animationDelay: '0.4s'}}>
          <div className="text-center p-6 bg-card/30 border border-primary/20 rounded-xl">
            <div className="text-2xl mb-2">🔒</div>
            <h4 className="font-semibold text-primary mb-2">Secure Payment</h4>
            <p className="text-sm text-muted-foreground">256-bit SSL encryption</p>
          </div>
          
          <div className="text-center p-6 bg-card/30 border border-primary/20 rounded-xl">
            <div className="text-2xl mb-2">⚡</div>
            <h4 className="font-semibold text-primary mb-2">Instant Delivery</h4>
            <p className="text-sm text-muted-foreground">PDF sent to your email immediately</p>
          </div>
          
          <div className="text-center p-6 bg-card/30 border border-primary/20 rounded-xl">
            <div className="text-2xl mb-2">🛡️</div>
            <h4 className="font-semibold text-primary mb-2">60-Day Guarantee</h4>
            <p className="text-sm text-muted-foreground">Full refund if not satisfied</p>
          </div>
        </div>
      </div>
    </section>
  );
};