import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";

interface PaymentFormProps {
  onSubmit: (data: { name: string; email: string }) => void;
  isLoading?: boolean;
}

export const PaymentForm = ({ onSubmit, isLoading }: PaymentFormProps) => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
  });
  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim() || !formData.email.trim()) {
      toast({
        title: "Required fields missing",
        description: "Please fill in both name and email.",
        variant: "destructive",
      });
      return;
    }

    if (!formData.email.includes("@")) {
      toast({
        title: "Invalid email",
        description: "Please enter a valid email address.",
        variant: "destructive",
      });
      return;
    }

    onSubmit(formData);
  };

  return (
    <div className="flex items-center justify-center h-screen">
    <Card className="bg-gradient-card border-primary/20 shadow-card scale-[1.2]">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-bold gradient-text">
          Get Your Copy Now
        </CardTitle>
        <p className="text-muted-foreground">
          Enter your details to proceed to payment
        </p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Full Name</Label>
            <Input
              id="name"
              type="text"
              placeholder="Enter your full name"
              value={formData.name}
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
              className="bg-secondary/50 border-primary/20 focus:border-primary"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              type="email"
              placeholder="Enter your email"
              value={formData.email}
              onChange={(e) =>
                setFormData({ ...formData, email: e.target.value })
              }
              className="bg-secondary/50 border-primary/20 focus:border-primary"
            />
          </div>

          <Button
            type="submit"
            className="w-full glow-button bg-gradient-primary hover:bg-primary/90 text-black font-semibold py-3"
            disabled={isLoading}
          >
            {isLoading ? "Processing..." : "Get Instant Access - $49"}
          </Button>
          
          <p className="text-xs text-muted-foreground text-center">
            Secure payment • Instant delivery • 60-day guarantee
          </p>
        </form>
      </CardContent>
    </Card>
    </div>
  );
};