export const TableOfContentsSection = () => {
  const chapters = [
    { number: "Foreword", title: "", pages: "Pg 3-4" },
    { number: "1.", title: "Why Networking Feels So Cringe (And How to Change That)", pages: "Pg 5-7" },
    { number: "2.", title: "The Psychology of First Impressions", pages: "Pg 8-10" },
    { number: "3.", title: "Metcalfe's Law: Why Every New Connection Increases Your Value", pages: "Pg 11-13" },
    { number: "4.", title: "The Hidden Currency of Attention", pages: "Pg 14-16" },
    { number: "5.", title: "Energy > Information: Reading Rooms, Not Just People", pages: "Pg 17-19" },
    { number: "6.", title: "The Anti-Sleaze Framework for Starting Conversations", pages: "Pg 20-23" },
    { number: "7.", title: "The Small Ask Strategy: How to Move From \"Hello\" to Hook", pages: "Pg 24-28" },
    { number: "8.", title: "How to Be Memorable (Even When You Don't Talk Much)", pages: "Pg 29-31" },
    { number: "9.", title: "Managing Social Anxiety While Networking", pages: "Pg 32-35" },
    { number: "10.", title: "Conversation Frameworks That Never Feel Awkward", pages: "Pg 36-37" },
    { number: "11.", title: "How to Follow Up Without Being Forgotten (or Annoying)", pages: "Pg 38-40" },
    { number: "12.", title: "The Introvert's Edge: Quiet Power in Loud Rooms", pages: "Pg 41-42" },
    { number: "13.", title: "Turning Weak Ties into Strong Allies", pages: "Pg 43-45" },
    { number: "14.", title: "How to Host Events That Magnetize People to You", pages: "Pg 46-48" },
    { number: "15.", title: "Networking Burnout: What It Is and How to Recover", pages: "Pg 49-52" },
    { number: "16.", title: "Building Serendipity Into Your Life (Without Being Fake About It)", pages: "Pg 53-55" },
    { number: "17.", title: "Mastering Group Dynamics (Without Feeling Invisible)", pages: "Pg 56-58" },
    { number: "18.", title: "Networking in the Digital World (Without Becoming Just Another DM)", pages: "Pg 59-61" },
    { number: "19.", title: "Building Your Personal Brand (Without Becoming a Content Clown)", pages: "Pg 62-64" },
    { number: "20.", title: "The ROI of Relationships (And How to Track It Without Feeling Gross)", pages: "Pg 65-67" }
  ];

  return (
    <section className="py-20 px-4">
      <div className="container mx-auto">
        <div className="text-start mb-10 fade-in-up">
          <h2 className="text-4xl lg:text-5xl font-bold mb-2">
            Table of <span className="gradient-text">Contents</span>
          </h2>
          <p className="text-md text-muted-foreground">
            20 tactical chapters designed for immediate implementation
          </p>
        </div>

        <div className="bg-gradient-card border border-grey-700 rounded-2xl p-8 fade-in-up" style={{animationDelay: '0.2s'}}>
          <div className="space-y-4">
            {chapters.map((chapter, index) => (
              <div 
                key={index}
                className="flex items-start justify-between py-3 border-b border-grey-700 last:border-b-0 fade-in-up"
                style={{animationDelay: `${0.3 + index * 0.03}s`}}
              >
                <div className="flex-1 pr-4">
                  <div className="flex items-start gap-3">
                    <span className="text-primary font-semibold text-sm min-w-[2rem]">
                      {chapter.number}
                    </span>
                    <span className="text-foreground/90 font-medium">
                      {chapter.title}
                    </span>
                  </div>
                </div>
                <span className="text-muted-foreground text-sm whitespace-nowrap">
                  {chapter.pages}
                </span>
              </div>
            ))}
          </div>
          
          <div className="mt-8 p-6 bg-primary/10 rounded-xl border border-primary/20">
            <p className="text-center text-foreground/90 font-medium">
              <span className="text-primary font-semibold">67 pages</span> of actionable frameworks, scripts, and real-world strategies
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};