import React from "react";
import { Carousel, Card } from "@/components/ui/apple-cards-carousel";
import { Spa<PERSON><PERSON>, Star, Zap } from "lucide-react";

const CarouselDemo = () => {
  const cards = [
    {
      src: "/placeholder-image-1.jpg",
      title: "Fast Performance",
      category: "Performance",
      content: (
        <div className="p-4">
          <Zap className="h-8 w-8 text-white mb-2" />
          <p className="text-white">Optimized React + Vite setup for lightning-fast development.</p>
        </div>
      ),
    },
    {
      src: "/placeholder-image-2.jpg",
      title: "Smooth Animations",
      category: "Animation",
      content: (
        <div className="p-4">
          <Sparkles className="h-8 w-8 text-white mb-2" />
          <p className="text-white">Powered by Framer Motion for beautiful, smooth animations.</p>
        </div>
      ),
    },
    {
      src: "/placeholder-image-3.jpg",
      title: "Beautiful UI",
      category: "Design",
      content: (
        <div className="p-4">
          <Star className="h-8 w-8 text-white mb-2" />
          <p className="text-white">Tailwind v4 + Aceternity Style for stunning interfaces.</p>
        </div>
      ),
    },
  ];

  return (
    <div className="w-full overflow-hidden bg-black py-20">
      <div className="mx-auto max-w-7xl px-4">
        <Carousel items={cards.map((card, index) => (
          <Card key={index} card={card} index={index} />
        ))} />
      </div>
    </div>
  );
};

export default CarouselDemo;
