"use client"; 

import { useState } from 'react';
import { load } from '@cashfreepayments/cashfree-js'; 
import { toast } from 'react-hot-toast'; 

export default function EbookCheckoutForm() {
  const [customerDetails, setCustomerDetails] = useState({ name: '', email: '', phone: '' });
  const [isLoading, setIsLoading] = useState(false);

  const handlePayment = async () => {
    // 1. Validate form fields
    if (!customerDetails.name || !customerDetails.email || !customerDetails.phone) {
        toast.error('Please fill in all details.');
        console.error('Please fill in all details.');
        return;
    }

    setIsLoading(true);
    try {
        // 2. Single API call to your backend to create the checkout session
        const response = await fetch('/api/checkout', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ customerDetails }),
        });

        const result = await response.json();
        if (!result.success) {
            throw new Error(result.error || 'Could not create payment session.');
        }

        const cashfree = await load({
            mode: process.env.NEXT_PUBLIC_CASHFREE_MODE === 'production' ? 'production' : 'sandbox'
        });

        await cashfree.checkout({
            paymentSessionId: result.paymentSessionId,
            redirectTarget: '_self'
        });

    } catch (error) {
        console.error('Payment error:', error);
        toast.error(error instanceof Error ? error.message : 'An unknown error occurred.');
    } finally {
        setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <input
        type="text"
        placeholder="Full Name"
        value={customerDetails.name}
        onChange={(e) => setCustomerDetails({ ...customerDetails, name: e.target.value })}
        className=" w-full p-3 border rounded-md "
        disabled={isLoading}
      />
      <input
        type="email"
        placeholder="Email Address"
        value={customerDetails.email}
        onChange={(e) => setCustomerDetails({ ...customerDetails, email: e.target.value })}
        className=" w-full p-3 border rounded-md "
        disabled={isLoading}
      />
      <input
        type="tel"
        placeholder="Phone Number"
        value={customerDetails.phone}
        onChange={(e) => setCustomerDetails({ ...customerDetails, phone: e.target.value })}
        className=" w-full p-3 border rounded-md "
        disabled={isLoading}
      />
      <button
        onClick={handlePayment}
        disabled={isLoading}
        className="w-full py-3 px-6 bg-white text-black font-semibold rounded-md isabled:bg-gray-400"
      >
        {isLoading ? 'Processing...' : 'Buy Now'}
      </button>
    </div>
  );
}