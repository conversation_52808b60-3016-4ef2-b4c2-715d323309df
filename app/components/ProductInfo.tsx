import { Play, Star } from "lucide-react";

interface Framework {
  icon: string;
  title: string;
  subtitle: string;
}

const frameworks: Framework[] = [
  {
    icon: "🧠",
    title: "Neural Rewiring",
    subtitle: "21 days to mastery.",
  },
  {
    icon: "🔓",
    title: "Desire Decoder",
    subtitle: "Unlock the primal drives.",
  },
  {
    icon: "🔗",
    title: "Bond Forger",
    subtitle: "Build unwavering loyalty.",
  }
];

const ProductInfo = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-4xl font-bold mb-4">The Puppet Master's Bible™</h1>
        <p className="text-neutral-100 mb-4">
          The rules were written against you. This system shows you how to rewrite them, break the system, and take control of your reality.
        </p>
      </div>

      {/* Frameworks */}
      <div>
        <h3 className="font-semibold mb-4">INCLUDES 8 FRAMEWORKS</h3>
        <div className="grid grid-cols-3 gap-4 mb-6">
          {frameworks.map((framework, index) => (
            <div key={index} className="text-center">
              <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-2xl">{framework.icon}</span>
              </div>
              <h4 className="font-semibold text-sm">{framework.title}</h4>
              <p className="text-xs text-gray-600">{framework.subtitle}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default ProductInfo; 