export const WhoIsThisForSection = () => {
  const targetAudience = [
    {
      title: "Entrepreneurs and operators",
      description: "who want to turn every room into a growth engine—by being the most strategic connector in it."
    },
    {
      title: "Professionals and leaders",
      description: "tired of transactional handshakes and empty LinkedIn likes—who want real allies, real deals, and real momentum."
    },
    {
      title: "Introverts and thinkers",
      description: "who want to master group dynamics, conversation design, and attention—without becoming someone they're not."
    },
    {
      title: "Anyone who wants to create a magnetic presence",
      description: "where people remember your name, respect your time, and bring you opportunities."
    }
  ];

  return (
    <section className="py-20 px-4 bg-secondary/20">
      <div className="container mx-auto max-w-4xl">
        <div className="text-center mb-16 fade-in-up">
          <h2 className="text-4xl lg:text-5xl font-bold mb-6">
            This Book Is <span className="gradient-text">For You</span>
          </h2>
          <p className="text-xl text-muted-foreground leading-relaxed">
            Let's be clear: This book isn't for passive readers.
          </p>
        </div>

        <div className="bg-card/50 border border-primary/20 rounded-2xl p-8 mb-12 fade-in-up" style={{animationDelay: '0.2s'}}>
          <p className="text-lg text-foreground/90 leading-relaxed mb-4">
            It's not a self-help pat on the back.
          </p>
          <p className="text-lg text-primary font-semibold">
            It's a field manual for those who want to build actual leverage—not by pretending, not by performing, but by becoming the kind of person others trust, remember, and move with.
          </p>
        </div>

        <div className="mb-12 fade-in-up" style={{animationDelay: '0.3s'}}>
          <h3 className="text-2xl font-bold text-center mb-8">This book is for:</h3>
          <div className="space-y-6">
            {targetAudience.map((item, index) => (
              <div 
                key={index}
                className="bg-gradient-card border border-primary/20 rounded-xl p-6 fade-in-up"
                style={{animationDelay: `${0.4 + index * 0.1}s`}}
              >
                <h4 className="text-lg font-semibold text-primary mb-2">
                  {item.title}
                </h4>
                <p className="text-muted-foreground">
                  {item.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        <div className="space-y-8 fade-in-up" style={{animationDelay: '0.8s'}}>
          <div className="bg-destructive/10 border border-destructive/30 rounded-xl p-6">
            <p className="text-lg text-foreground/90 leading-relaxed">
              If you're content floating through events, collecting business cards you'll never use, and hoping one day your talent will "speak for itself," this book isn't for you.
            </p>
          </div>
          
          <div className="bg-primary/10 border border-primary/30 rounded-xl p-6">
            <p className="text-lg font-semibold text-primary mb-2">
              But if you're ready to move intentionally, build relationships that compound, and design a network that actually works for you—
            </p>
            <p className="text-lg text-foreground/90">
              then welcome to the Dominion way.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};