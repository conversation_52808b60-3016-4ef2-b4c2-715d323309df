import { GoogleSpreadsheet } from 'google-spreadsheet';
import { JWT } from 'google-auth-library';

// google Sheets authentication
export function createGoogleSheetsAuth() {
  return new JWT({
    email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL!,
    key: process.env.GOOGLE_PRIVATE_KEY!.replace(/\\n/g, '\n'),
    scopes: ['https://www.googleapis.com/auth/spreadsheets'],
  });
}


//spreadsheet instance :)
export function getGoogleSpreadsheet() {
  const auth = createGoogleSheetsAuth();
  return new GoogleSpreadsheet(process.env.GOOGLE_SHEET_ID!, auth);
}


// Types
export interface FormSubmissionData {
  name: string;
  email: string;
  uniqueID?: string;
  status?: string;
  orderID?: string;
  paymentID?: string;
  timestamp?: string;
}



//new form submission 
export async function addFormSubmission(data: FormSubmissionData) {
  const doc = getGoogleSpreadsheet();
  await doc.loadInfo();
  const sheet = doc.sheetsByIndex[0];

  const rowData = {
    Timestamp: data.timestamp || new Date().toLocaleString(),
    Name: data.name.trim(),
    Email: data.email.toLowerCase().trim(),
    UniqueID: data.uniqueID || '',
    Status: data.status || 'Pending Payment',
    OrderID: data.orderID || '',
    PaymentID: data.paymentID || '',
    EmailSent: 'No',
  };

  await sheet.addRow(rowData);
  return rowData;
}

// payment status updation
export async function updatePaymentStatus(
  uniqueID: string, 
  orderID: string, 
  paymentID: string
) {
  const doc = getGoogleSpreadsheet();
  await doc.loadInfo();
  const sheet = doc.sheetsByIndex[0];

  const rows = await sheet.getRows();
  const targetRow = rows.find(row => row.get('UniqueID') === uniqueID);
  
  if (!targetRow) {
    throw new Error('Record not found with provided UniqueID');
  }

  targetRow.set('Status', 'Payment Complete');
  targetRow.set('OrderID', orderID);
  targetRow.set('PaymentID', paymentID);
  targetRow.set('Timestamp', new Date().toLocaleString());
  
  await targetRow.save();
  
  return {
    uniqueID,
    status: 'Payment Complete',
    orderID,
    paymentID,
    timestamp: new Date().toLocaleString()
  };
}

//update payment status by email (for webhook usage)
export async function updatePaymentStatusByEmail(
  email: string,
  orderID: string,
  paymentID: string
) {
  const doc = getGoogleSpreadsheet();
  await doc.loadInfo();
  const sheet = doc.sheetsByIndex[0];

  const rows = await sheet.getRows();
  const normalizedEmail = email.toLowerCase().trim();

  //finding  specific row with pending payment ^-^
  const targetRow = rows.find(row => {
    const rowEmail = row.get('Email')?.toLowerCase().trim();
    const rowStatus = row.get('Status')?.trim();
    return rowEmail === normalizedEmail && rowStatus === 'Pending Payment';
  });

  if (!targetRow) {
    throw new Error(`No pending payment record found for email: ${normalizedEmail}`);
  }

  targetRow.set('Status', 'Payment Complete');
  targetRow.set('OrderID', orderID);
  targetRow.set('PaymentID', paymentID);
  targetRow.set('Timestamp', new Date().toLocaleString());

  await targetRow.save();

  return {
    email: normalizedEmail,
    uniqueID: targetRow.get('UniqueID'),
    status: 'Payment Complete',
    orderID,
    paymentID,
    timestamp: new Date().toLocaleString()
  };
}

// marking email as sending
export async function markEmailAsSent(email: string, orderID: string) {
  const doc = getGoogleSpreadsheet();
  await doc.loadInfo();
  const sheet = doc.sheetsByIndex[0];

  const rows = await sheet.getRows();
  const normalizedEmail = email.toLowerCase().trim();

  // Find the row with this email and order ID
  const targetRow = rows.find(row => {
    const rowEmail = row.get('Email')?.toLowerCase().trim();
    const rowOrderID = row.get('OrderID')?.trim();
    return rowEmail === normalizedEmail && rowOrderID === orderID;
  });

  if (targetRow) {
    targetRow.set('EmailSent', 'Yes');
    await targetRow.save();
    return true;
  }

  return false;
}


export async function wasEmailAlreadySent(email: string, orderID: string): Promise<boolean> {
  try {
    const doc = getGoogleSpreadsheet();
    await doc.loadInfo();
    const sheet = doc.sheetsByIndex[0];

    const rows = await sheet.getRows();
    const normalizedEmail = email.toLowerCase().trim();


    const targetRow = rows.find(row => {
      const rowEmail = row.get('Email')?.toLowerCase().trim();
      const rowOrderID = row.get('OrderID')?.trim();
      return rowEmail === normalizedEmail && rowOrderID === orderID;
    });

    if (targetRow) {
      const emailSent = targetRow.get('EmailSent')?.trim();


      return emailSent === 'Yes';
    }

    return false;
  } catch (error) {
    
    return false;
  }
}
