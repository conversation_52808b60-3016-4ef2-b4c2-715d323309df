import { Cashfree, CFEnvironment } from "cashfree-pg";

// This check ensures this code only runs on the server, which is where it belongs.
// Your secret keys should never be exposed to the client-side browser.
if (typeof window !== 'undefined') {
  throw new Error("The Cashfree server-side SDK cannot be used in the browser.");
}

// 1. Validate that your secret credentials are available in your environment variables.
// This is a crucial check to prevent your app from crashing at runtime if a key is missing.
if (!process.env.CASHFREE_APP_ID || !process.env.CASHFREE_SECRET_KEY) {
  throw new Error(
    "Cashfree API credentials are not set in the environment variables. " +
    "Please define CASHFREE_APP_ID and CASHFREE_SECRET_KEY."
  );
}

// 2. Determine the Cashfree environment (sandbox or production)
// This reads from a public environment variable so you can easily switch modes.
const cashfreeEnvironment = process.env.NEXT_PUBLIC_CASHFREE_MODE === 'production'
  ? CFEnvironment.PRODUCTION
  : CFEnvironment.SANDBOX;

// 3. Initialize the Cashfree SDK instance with your credentials and the chosen mode.
// This is the single instance that your entire backend will use.
const cashfree = new Cashfree(
  cashfreeEnvironment,
  process.env.CASHFREE_APP_ID,
  process.env.CASHFREE_SECRET_KEY
);

// We log the mode once when the server starts up for easier debugging.
console.log(`Cashfree server-side SDK initialized in '${process.env.NEXT_PUBLIC_CASHFREE_MODE || 'sandbox'}' mode.`);

// 4. Export the configured instance as the default export.
// This allows other files to import it with `import cashfree from "@/app/lib/cashfree";`.
export default cashfree;