import nodemailer from 'nodemailer';
import { promises as fs } from 'fs';
import path from 'path';

export async function sendEbook(email: string, name: string) {
  console.log(`sendEbook function started for ${email}.`);
  try {
    const filePath = path.join(process.cwd(), 'protected', 'ebook.pdf');
    console.log(`Attempting to read e-book file from: ${filePath}`);
    const pdfBuffer = await fs.readFile(filePath);
    console.log('E-book file read successfully.');

    console.log('Creating Nodemailer transporter...');
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });
    console.log('Nodemailer transporter created.');

    console.log(`Attempting to send email to ${email}...`);
    await transporter.sendMail({
      from: `"E-Book Dominion" <${process.env.EMAIL_USER}>`,
      to: email,
      subject: 'Your E-Book Download',
      text: `Hi ${name},\n\nThanks for your purchase! Your e-book is attached.`,
      attachments: [
        {
          filename: 'ebook.pdf',
          content: pdfBuffer,
        },
      ],
    });
    console.log(`E-book sent to ${email}`);
    return { success: true };
  } catch (err) {
    console.error('CRITICAL: Email sending failed inside sendEbook function:', err);
    return { success: false, error: 'Failed to send email' };
  }
} 