"use client"

import Header from './components/Header';
import BookPreview from './components/BookPreview';
import ProductInfo from './components/ProductInfo';
import CtaButton from './components/CtaButton';
import ShippingInfo from './components/ShippingInfo';
import WhyItWorksSection from './components/WhyItWorksSection';
import {TableOfContentsSection} from './components/TableOfContentsSection';
import {AppleCardsCarouselDemo} from './components/AppCardsCarouselDemo';
import {FAQSection} from './components/FAQSection';



const PuppetMastersBible = () => {

  return (
    <div className="min-h-screen bg-black text-white font-['Plus_Jakarta_Sans']">
      <Header />

      <div className="max-w-8xl mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-2 gap-8 items-start">
          <BookPreview />
          <div className="space-y-6">
            <ProductInfo />
            <CtaButton />
            <ShippingInfo />
          </div>
        </div>
         <WhyItWorksSection />
            {/* <WhoIsThisForSection /> */}
            <TableOfContentsSection />
            {/* <GuaranteeSection /> */}
            <AppleCardsCarouselDemo />
            <FAQSection />
      </div>
    </div>
  );
};

export default PuppetMastersBible;