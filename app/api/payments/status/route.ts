import { NextRequest, NextResponse } from 'next/server';
import cashfree from '@/app/lib/cashfree';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const orderId = searchParams.get('order_id');

  if (!orderId) {
    return NextResponse.json({ error: 'Order ID is required' }, { status: 400 });
  }

  try {
    const order = await cashfree.PGOrderFetchPayments(orderId);
    return NextResponse.json({ payment_status: order.data[0].payment_status });
  } catch (error) {
    console.error('Failed to fetch order status:', error);
    return NextResponse.json({ error: 'Failed to fetch order status' }, { status: 500 });
  }
} 