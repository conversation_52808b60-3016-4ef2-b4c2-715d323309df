import { NextRequest, NextResponse } from 'next/server';
import { sendEbook } from '@/app/lib/email';
import { updatePaymentStatusByEmail, wasEmailAlreadySent, markEmailAsSent } from '@/app/lib/google-sheets';

// In-memory store to track processed payments (in production, use Redis or database)
const processedPayments = new Set<string>();

// Clean up old processed payments every hour to prevent memory leaks
setInterval(() => {
  processedPayments.clear();
  console.log('Cleared processed payments cache');
}, 60 * 60 * 1000); // 1 hour

export async function POST(request: NextRequest) {
  try {
    const webhookData = await request.json();
    const { order, payment, customer_details } = webhookData.data;

    if (payment && payment.payment_status === 'SUCCESS') {
      const customerEmail = customer_details.customer_email?.toLowerCase().trim();
      const customerName = customer_details.customer_name || 'Valued Customer';
      const orderId = order.order_id;
      const paymentId = payment.cf_payment_id;

      // Create a unique key for this payment to prevent duplicate processing
      const paymentKey = `${orderId}-${paymentId}-${customerEmail}`;

      // Check if we already processed this exact payment
      if (processedPayments.has(paymentKey)) {
        return NextResponse.json({ success: true, message: 'Already processed' });
      }

      // Check if email was already sent for this order
      const emailAlreadySent = await wasEmailAlreadySent(customerEmail, orderId);
      if (emailAlreadySent) {
        processedPayments.add(paymentKey);
        return NextResponse.json({ success: true, message: 'Email already sent' });
      }

      // Try to update Google Sheets (this will only work if there's a pending payment)
      try {
        await updatePaymentStatusByEmail(customerEmail, orderId, paymentId);

        // If sheets update succeeded, send email
        const emailResult = await sendEbook(customerEmail, customerName);

        if (emailResult.success) {
          // Mark email as sent in Google Sheets
          await markEmailAsSent(customerEmail, orderId);
          // Mark this payment as processed to prevent future duplicates
          processedPayments.add(paymentKey);
        }

      } catch (sheetError) {
        // If no pending record found, mark as processed to prevent retries
        if (sheetError instanceof Error && sheetError.message.includes('No pending payment record found')) {
          processedPayments.add(paymentKey);
        }
        // For other errors, don't mark as processed so it can be retried
      }
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Webhook processing failed:', error);
    return NextResponse.json({ success: false, error: 'Webhook processing failed' }, { status: 400 });
  }
}