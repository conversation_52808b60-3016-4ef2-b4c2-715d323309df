import { NextRequest, NextResponse } from "next/server";
import cashfree from "@/app/lib/cashfree";
import { EBOOK_CONFIG } from "@/app/constants/product";
import { addFormSubmission } from "@/app/lib/google-sheets";
import { v4 as uuidv4 } from 'uuid';

export async function POST(request: NextRequest) {
  try {
    const { customerDetails } = await request.json();

    if (!customerDetails?.name || !customerDetails?.email || !customerDetails?.phone) {
      return NextResponse.json({ error: "Name, email, and phone are required" }, { status: 400 });
    }

    const orderAmount = EBOOK_CONFIG.price;
    const orderCurrency = EBOOK_CONFIG.currency_code;

    const order_id = `ebook-order-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    const uniqueID = uuidv4(); // Generate unique ID for Google Sheets tracking

    // Create initial entry in Google Sheets
    try {
      await addFormSubmission({
        name: customerDetails.name,
        email: customerDetails.email,
        uniqueID: uniqueID,
        status: 'Pending Payment'
      });
      console.log(`Google Sheets entry created for ${customerDetails.email} with UniqueID: ${uniqueID}`);
    } catch (sheetError) {
      console.error('Failed to create Google Sheets entry:', sheetError);
      // Continue with payment creation even if sheets fails
    }

    const orderRequest = {
      order_id: order_id,
      order_amount: orderAmount,
      order_currency: orderCurrency,
      customer_details: {
        customer_id: `customer-${customerDetails.phone}`,
        customer_phone: customerDetails.phone,
        customer_email: customerDetails.email,
        customer_name: customerDetails.name,
      },
      order_meta: {
        return_url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/status?order_id={order_id}`,
        notify_url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/payments/webhooks`,
      },
      order_note: 'E-book purchase',
    };

    const response = await cashfree.PGCreateOrder(orderRequest);

    if (!response.data || !response.data.payment_session_id) {
        throw new Error("Failed to get payment_session_id from Cashfree");
    }

    return NextResponse.json({
      success: true,
      paymentSessionId: response.data.payment_session_id,
      uniqueID: uniqueID // Return the unique ID for potential future use
    });

  } catch (error) {
    console.error("Error creating payment session:", error);
    return NextResponse.json({ error: "Failed to create payment session" }, { status: 500 });
  }
}