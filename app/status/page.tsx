"use client";

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { CheckCircle, XCircle, Clock } from 'lucide-react';

function StatusContent() {
  const searchParams = useSearchParams();
  const orderId = searchParams.get('order_id');
  const [status, setStatus] = useState('loading');
  const [message, setMessage] = useState('Verifying payment status...');

  useEffect(() => {
    if (orderId) {
      const verifyPayment = async () => {
        try {
          const response = await fetch(`/api/payments/status?order_id=${orderId}`);
          const result = await response.json();

          if (response.ok) {
            setStatus(result.payment_status.toLowerCase());
            switch (result.payment_status) {
              
              case 'SUCCESS':
                setMessage('Payment successful! Your e-book is on its way.');
                break;
              case 'FAILURE':
                setMessage('Payment failed. Please try again.');
                break;
              case 'PENDING':
                setMessage('Payment is pending. We will notify you once it is confirmed.');
                break;
              default:
                setMessage('An unknown error occurred.');
            }
          } else {
            throw new Error(result.error || 'Failed to verify payment status.');
          }
        } catch (error) {
          setStatus('error');
          setMessage(error instanceof Error ? error.message : 'An unknown error occurred.');
          toast.error(error instanceof Error ? error.message : 'An unknown error occurred.');
        }
      };
      verifyPayment();
    }
  }, [orderId]);

  const renderIcon = () => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-16 h-16 text-green-500" />;
      case 'failure':
        return <XCircle className="w-16 h-16 text-red-500" />;
      case 'pending':
        return <Clock className="w-16 h-16 text-yellow-500" />;
      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      {renderIcon()}
      <h1 className="text-2xl font-bold mt-4">{message}</h1>
      <p className="text-gray-500 mt-2">Order ID: {orderId}</p>
    </div>
  );
}

export default function StatusPage() {
    return (
      <Suspense fallback={<div>Loading...</div>}>
        <StatusContent />
      </Suspense>
    );
  }